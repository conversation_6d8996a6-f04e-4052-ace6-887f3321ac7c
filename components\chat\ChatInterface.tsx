"use client"

import { useState, useCallback } from "react"
import { Message } from "@/types/chat"
import { MessageList } from "./MessageList"
import { ChatInput } from "./ChatInput"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Plus, ChevronDown } from "lucide-react"

type ModelOption = {
  id: string
  name: string
  description: string
}

const modelOptions: ModelOption[] = [
  {
    id: "gemini-2.5-flash-preview-05-20",
    name: "Gemini 2.5 Flash Preview",
    description: "Latest preview with enhanced capabilities"
  },
  {
    id: "gemini-2.5-pro-preview-05-06",
    name: "Gemini 2.5 Pro",
    description: "Most capable model"
  },
  {
    id: "gemini-2.5-flash",
    name: "Gemini 2.5 Flash",
    description: "Faster responses"
  }
]

export function ChatInterface() {
  const [messages, setMessages] = useState<Message[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedModel, setSelectedModel] = useState<ModelOption>(modelOptions[0])

  // Generate a unique ID for messages
  const generateMessageId = () => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  // Call Vertex AI API
  const callVertexAI = useCallback(async (userMessage: string): Promise<string> => {
    try {
      // Prepare chat history for API
      const history = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }))

      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage,
          model: selectedModel.id,
          history: history,
          stream: false
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to get response from AI')
      }

      const data = await response.json()
      return data.content || 'Sorry, I could not generate a response.'
    } catch (error) {
      console.error('Vertex AI API error:', error)
      throw error
    }
  }, [selectedModel.id, messages])

  const handleSendMessage = useCallback(async (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: generateMessageId(),
      content,
      role: 'user',
      timestamp: new Date(),
      status: 'sent'
    }

    setMessages(prev => [...prev, userMessage])
    setIsLoading(true)

    try {
      // Call Vertex AI API
      const aiResponse = await callVertexAI(content)

      // Add AI message
      const aiMessage: Message = {
        id: generateMessageId(),
        content: aiResponse,
        role: 'assistant',
        timestamp: new Date(),
        status: 'sent'
      }

      setMessages(prev => [...prev, aiMessage])
    } catch (error) {
      // Handle error
      const errorMessage: Message = {
        id: generateMessageId(),
        content: "I apologize, but I encountered an error while processing your request. Please try again.",
        role: 'assistant',
        timestamp: new Date(),
        status: 'error'
      }

      setMessages(prev => [...prev, errorMessage])
    } finally {
      setIsLoading(false)
    }
  }, [callVertexAI])

  const handleNewChat = () => {
    setMessages([])
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-4 py-3">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div>
              <div className="flex items-center space-x-2">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-7 px-3 text-xs font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 border border-gray-200 rounded-md"
                    >
                      {selectedModel.name}
                      <ChevronDown className="w-3 h-3 ml-2" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-56">
                    {modelOptions.map((model) => (
                      <DropdownMenuItem
                        key={model.id}
                        onClick={() => setSelectedModel(model)}
                        className="flex flex-col items-start py-2"
                      >
                        <div className="font-medium text-sm">{model.name}</div>
                        <div className="text-xs text-gray-500">{model.description}</div>
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
                <span className="text-xs text-gray-500">Ready to help</span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNewChat}
              className="text-gray-600 hover:text-gray-900"
            >
              <Plus className="w-4 h-4 mr-2" />
              New Chat
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-600 hover:text-gray-900"
            >
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-gray-600 hover:text-gray-900"
            >
            </Button>
          </div>
        </div>
      </header>

      {/* Messages Area */}
      <MessageList
        messages={messages}
        isLoading={isLoading}
        onSendMessage={handleSendMessage}
      />

      {/* Input Area */}
      <ChatInput
        onSendMessage={handleSendMessage}
        disabled={isLoading}
        placeholder="Ask me anything..."
      />
    </div>
  )
}
