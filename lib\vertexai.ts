import { Vertex<PERSON><PERSON> } from '@google-cloud/vertexai';

// Initialize Vertex AI
const vertexAI = new VertexAI({
  project: process.env.GOOGLE_CLOUD_PROJECT_ID!,
  location: process.env.GOOGLE_CLOUD_LOCATION || 'us-central1',
});

// Available Vertex AI models
export const VERTEX_AI_MODELS = {
  GEMINI_2_5_FLASH_PREVIEW: 'gemini-2.5-flash-preview-05-20',
  GEMINI_2_5_PRO: 'gemini-2.5-pro',
  GEMINI_2_5_FLASH: 'gemini-2.5-flash',
} as const;

export type VertexAIModel = typeof VERTEX_AI_MODELS[keyof typeof VERTEX_AI_MODELS];

// Configuration for different models
const MODEL_CONFIGS = {
  [VERTEX_AI_MODELS.GEMINI_2_5_FLASH_PREVIEW]: {
    temperature: 0.7,
    topP: 0.8,
    topK: 40,
    maxOutputTokens: 8192,
  },
  [VERTEX_AI_MODELS.GEMINI_2_5_PRO]: {
    temperature: 0.7,
    topP: 0.8,
    topK: 40,
    maxOutputTokens: 8192,
  },
  [VERTEX_AI_MODELS.GEMINI_2_5_FLASH]: {
    temperature: 0.7,
    topP: 0.8,
    topK: 40,
    maxOutputTokens: 8192,
  },
};

export interface ChatMessage {
  role: 'user' | 'model';
  parts: Array<{ text: string }>;
}

export interface GenerateContentRequest {
  model: VertexAIModel;
  messages: ChatMessage[];
  systemInstruction?: string;
}

export interface GenerateContentResponse {
  content: string;
  finishReason?: string;
  safetyRatings?: any[];
}

/**
 * Generate content using Vertex AI Gemini models
 */
export async function generateContent({
  model,
  messages,
  systemInstruction,
}: GenerateContentRequest): Promise<GenerateContentResponse> {
  try {
    // Get the generative model
    const generativeModel = vertexAI.getGenerativeModel({
      model: model,
      generationConfig: MODEL_CONFIGS[model],
      systemInstruction: systemInstruction ? {
        parts: [{ text: systemInstruction }]
      } : undefined,
    });

    // Convert messages to the format expected by Vertex AI
    const contents = messages.map(msg => ({
      role: msg.role,
      parts: msg.parts,
    }));

    // Generate content
    const result = await generativeModel.generateContent({
      contents,
    });

    const response = result.response;
    const text = response.candidates?.[0]?.content?.parts?.[0]?.text || '';

    return {
      content: text,
      finishReason: response.candidates?.[0]?.finishReason,
      safetyRatings: response.candidates?.[0]?.safetyRatings,
    };
  } catch (error) {
    console.error('Vertex AI generation error:', error);
    throw new Error(`Failed to generate content: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Generate streaming content using Vertex AI Gemini models
 */
export async function* generateContentStream({
  model,
  messages,
  systemInstruction,
}: GenerateContentRequest): AsyncGenerator<string, void, unknown> {
  try {
    // Get the generative model
    const generativeModel = vertexAI.getGenerativeModel({
      model: model,
      generationConfig: MODEL_CONFIGS[model],
      systemInstruction: systemInstruction ? {
        parts: [{ text: systemInstruction }]
      } : undefined,
    });

    // Convert messages to the format expected by Vertex AI
    const contents = messages.map(msg => ({
      role: msg.role,
      parts: msg.parts,
    }));

    // Generate streaming content
    const streamingResult = await generativeModel.generateContentStream({
      contents,
    });

    for await (const chunk of streamingResult.stream) {
      const text = chunk.candidates?.[0]?.content?.parts?.[0]?.text || '';
      if (text) {
        yield text;
      }
    }
  } catch (error) {
    console.error('Vertex AI streaming error:', error);
    throw new Error(`Failed to generate streaming content: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Validate if the required environment variables are set
 */
export function validateVertexAIConfig(): boolean {
  return !!(
    process.env.GOOGLE_CLOUD_PROJECT_ID &&
    (process.env.GOOGLE_APPLICATION_CREDENTIALS || process.env.GOOGLE_CLOUD_CREDENTIALS)
  );
}

/**
 * Get model information
 */
export function getModelInfo(model: VertexAIModel) {
  const configs = {
    [VERTEX_AI_MODELS.GEMINI_2_5_FLASH_PREVIEW]: {
      name: 'Gemini 2.5 Flash Preview',
      description: 'Latest preview version of Gemini 2.5 Flash with enhanced capabilities',
      contextLength: '1M tokens',
      features: ['Text', 'Code', 'Analysis', 'Fast responses'],
    },
    [VERTEX_AI_MODELS.GEMINI_2_5_PRO]: {
      name: 'Gemini 2.5 Pro',
      description: 'Most capable Gemini model for complex tasks',
      contextLength: '1M tokens',
      features: ['Text', 'Images', 'Video', 'Audio', 'Code', 'Math'],
    },
    [VERTEX_AI_MODELS.GEMINI_2_5_FLASH]: {
      name: 'Gemini 2.5 Flash',
      description: 'Fast and efficient Gemini model',
      contextLength: '1M tokens',
      features: ['Text', 'Code', 'Fast responses'],
    },
  };

  return configs[model];
}
